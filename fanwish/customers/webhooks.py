import json
import logging

import stripe
from django.conf import settings
from django.http import HttpResponse, HttpResponseBadRequest
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt


logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class StripeWebhookView(View):
    """Handle Stripe webhooks"""

    def post(self, request):
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        endpoint_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')

        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except ValueError:
            logger.error("Invalid payload in Stripe webhook")
            return HttpResponseBadRequest("Invalid payload")
        except stripe.error.SignatureVerificationError:
            logger.error("Invalid signature in Stripe webhook")
            return HttpResponseBadRequest("Invalid signature")

        # Handle the event
        if event['type'] == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            self.handle_payment_success(payment_intent)
        elif event['type'] == 'payment_intent.payment_failed':
            payment_intent = event['data']['object']
            self.handle_payment_failed(payment_intent)
        else:
            logger.info(f"Unhandled Stripe event type: {event['type']}")

        return HttpResponse(status=200)

    def handle_payment_success(self, payment_intent):
        """Handle successful payment"""
        try:
            payment_intent_id = payment_intent['id']
            order = PaymentService.handle_payment_success(payment_intent_id)

            if order:
                logger.info(f"Payment successful for order {order.order_number}")
                # Here you could send notifications, emails, etc.
            else:
                logger.error(f"Order not found for payment intent {payment_intent_id}")

        except Exception as e:
            logger.error(f"Error handling payment success: {str(e)}")

    def handle_payment_failed(self, payment_intent):
        """Handle failed payment"""
        try:
            payment_intent_id = payment_intent['id']
            logger.warning(f"Payment failed for payment intent {payment_intent_id}")

            # You could update order status, send notifications, etc.

        except Exception as e:
            logger.error(f"Error handling payment failure: {str(e)}")
