from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework.decorators import action
from rest_framework.mixins import ListModelMixin, RetrieveModelMixin, CreateModelMixin, UpdateModelMixin
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from fanwish.customers.authentication import CustomerTokenAuthentication
from fanwish.orders.api.serializers import (
    PublicOrderSerializer,
    OrderSerializer,
)
from fanwish.orders.models import Order
from fanwish.orders.services import OrderService


@extend_schema_view(
    list=extend_schema(summary="获取我的订单列表"),
    confirm_order=extend_schema(summary="确认订单"),
)
class OrderViewSet(ListModelMixin, GenericViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user).exclude(order_status=Order.OrderStatus.PENDING_PAYMENT).order_by('-id')

    @action(detail=True, methods=['post'], url_path="confirm")
    def confirm_order(self, request: Request):
        order = self.get_object()
        OrderService.confirm_order(order)
        serializer = self.get_serializer(order)
        return Response(serializer.data)


@extend_schema_view(
    create=extend_schema(summary="创建订单（粉丝下单）"),
    retrieve=extend_schema(summary="获取订单详情"),
    partial_update=extend_schema(summary="更新订单"),
)
class PublicOrderViewSet(CreateModelMixin, RetrieveModelMixin, UpdateModelMixin, GenericViewSet):
    queryset = Order.objects.all()
    authentication_classes = [CustomerTokenAuthentication]
    serializer_class = PublicOrderSerializer

    def get_queryset(self):
        return self.queryset.filter(customer=self.request.user)

    def perform_create(self, serializer):
        order = OrderService.create_order(serializer.validated_data)
        serializer.instance = order
