import stripe
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from stripe import StripeClient

from fanwish.common.exceptions import ValidationError
from fanwish.orders.models import Order
from fanwish.products.providers import get_product_provider_by_name


class OrderService:

    @staticmethod
    def create_order(order_date: dict) -> Order:
        redirect_url = order_date.pop('redirect_url')
        dry_run = order_date.pop('dry_run', False)
        order = Order(**order_date)
        user = order.user
        customer = order.customer

        order.address = Order.Address(
            id=user.address.id,
            name=user.address.name,
            line_1=user.address.line_1,
            line_2=user.address.line_2,
            country=user.address.country,
            state=user.address.state,
            city=user.address.city,
            postal_code=user.address.postal_code,
            phone=user.address.phone,
            tax_rate=user.address.tax_rate,
        )

        if not dry_run:
            customer.update_social_info(order.customer_name_type, order.customer_name)

            stripe_client = StripeClient(settings.STRIPE_SECRET_KEY, http_client=stripe.RequestsClient())

            stripe_line_items = []
            stripe_common_metadata = {
                'order_id': order.id,
                'customer_id': customer.id,
                'user_id': user.id,
            }
            # Stripe line items
            for item in order.line_items:
                stripe_metadata = stripe_common_metadata.copy()
                stripe_metadata['gift_item_id'] = item.id
                stripe_product = stripe_client.products.create(
                    params={
                        "name": item.title,
                        "images": [item.image],
                        "metadata": stripe_metadata,
                        "default_price_data": {
                            "currency": item.display_price.currency.lower(),
                            "unit_amount": item.display_price.amount_in_cents,
                            "metadata": stripe_metadata,
                        },
                    },
                )
                stripe_line_items.append({
                    "price": stripe_product.default_price,
                    "quantity": 1,
                })
            # Stripe shipping costs
            stripe_shipping_product = stripe_client.products.create(
                params={
                    "name": _("Shipping Costs"),
                    "metadata": stripe_common_metadata,
                    "default_price_data": {
                        "currency": order.subtotal_shipping_cost.currency.lower(),
                        "unit_amount": order.subtotal_shipping_cost.amount_in_cents,
                        "metadata": stripe_common_metadata,
                    },
                },
            )
            stripe_line_items.append({
                "price": stripe_shipping_product.default_price,
                "quantity": 1,
            })
            # Stripe taxes
            stripe_tax_product = stripe_client.products.create(
                params={
                    "name": _("Tax"),
                    "metadata": stripe_common_metadata,
                    "default_price_data": {
                        "currency": order.subtotal_tax_amount.currency.lower(),
                        "unit_amount": order.subtotal_tax_amount.amount_in_cents,
                        "metadata": stripe_common_metadata,
                    },
                },
            )
            stripe_line_items.append({
                "price": stripe_tax_product.default_price,
                "quantity": 1,
            })
            # Stripe service fee
            stripe_service_fee_product = stripe_client.products.create(
                params={
                    "name": _("Privacy & Security fee"),
                    "metadata": stripe_common_metadata,
                    "default_price_data": {
                        "currency": order.service_fee.currency.lower(),
                        "unit_amount": order.service_fee.amount_in_cents,
                        "metadata": stripe_common_metadata,
                    },
                },
            )
            stripe_line_items.append({
                "price": stripe_service_fee_product.default_price,
                "quantity": 1,
            })
            payment_link = stripe_client.payment_links.create(
                params={
                    "line_items": stripe_line_items,
                    "metadata": stripe_common_metadata,
                    "after_completion": {
                        "type": "redirect",
                        "redirect": {
                            "url": f"{redirect_url}&order_id={order.id}&checkout_session_id={{CHECKOUT_SESSION_ID}}",
                        },
                    },
                    "billing_address_collection": "auto",
                    # PaymentLink 总是创建新的 Customer
                    # "customer_creation": "always",
                    "payment_intent_data": {
                        "metadata": stripe_common_metadata,
                    },
                    "restrictions": {
                        "completed_sessions": {
                            "limit": 1,
                        }
                    }
                }
            )

            order.payment_link = payment_link.url
            order.save()

        return order

    @staticmethod
    def confirm_order(order: Order) -> Order:
        line_item_providers = set(li.product_provider for li in order.line_items)
        if len(line_item_providers) != 1:
            # todo 若商品跨多个平台，需要遍历 line_items 下单
            raise ValidationError()

        if order.order_status != Order.OrderStatus.PENDING_CONFIRMATION:
            raise ValidationError("Order is not in pending confirmation status")

        provider = get_product_provider_by_name(list(line_item_providers)[0])
        provider.create_order(order)

        return order
