import re
import typing
from decimal import Decimal
from urllib.parse import urlparse

import requests
from django.conf import settings
from django.utils import timezone

from fanwish.common.exceptions import InvalidProductURLError, UnsupportedProductPlatformError
from fanwish.common.exceptions import ValidationError
from fanwish.common.money import Money
from fanwish.products.models import ProductProvider, Product
from fanwish.utils.caches import products_cache
from fanwish.utils.logging import Logger

if typing.TYPE_CHECKING:
    from fanwish.orders.models import Order as OrderType


class AmazonProvider(ProductProvider):
    class Region:
        """https://developer-docs.amazon.com/amazon-business/docs/product-search-api-v1-reference#productregion"""
        US = "US"
        CA = "CA"
        UK = "UK"
        ES = "ES"
        FR = "FR"
        DE = "DE"
        IT = "IT"
        JP = "JP"

    API_ENDPOINTS = {
        # https://developer-docs.amazon.com/amazon-business/docs/ab-api-endpoints
        Region.US: "https://na.business-api.amazon.com",
        Region.CA: "https://na.business-api.amazon.com",
        Region.UK: "https://eu.business-api.amazon.com",
        Region.ES: "https://eu.business-api.amazon.com",
        Region.FR: "https://eu.business-api.amazon.com",
        Region.DE: "https://eu.business-api.amazon.com",
        Region.IT: "https://eu.business-api.amazon.com",
        Region.JP: "https://jp.business-api.amazon.com",
    }

    DEFAULT_CURRENCIES = {
        Region.US: 'USD',
        Region.CA: 'CAD',
        Region.UK: 'GBP',
        Region.ES: 'EUR',
        Region.FR: 'EUR',
        Region.DE: 'EUR',
        Region.IT: 'EUR',
        Region.JP: 'JPY',
    }

    def __init__(self, region: str):
        self.region = region.upper()
        self.default_currency = self.DEFAULT_CURRENCIES[self.region]
        self.api_endpoint = self.API_ENDPOINTS[self.region]
        self.refresh_token = settings.AMAZON_BUSINESS_API_REFRESH_TOKENS[self.region]
        self.access_token = ''
        self.logger = Logger.get_app_logger("AmazonProvider", extra={"amazon.region": self.region})
        if not self.refresh_token:
            self.logger.error(f"Missing refresh token for {self.region}")

    def get_provider_name(self) -> str:
        return f"amazon_{self.region.lower()}"

    def extract_id_from_url(self, url: str) -> str:
        """Extract ASIN from Amazon product URL"""
        patterns = [
            r'/dp/([A-Z0-9]{10})',
            r'/gp/product/([A-Z0-9]{10})',
            r'/product/([A-Z0-9]{10})',
            r'asin=([A-Z0-9]{10})',
            r'/([A-Z0-9]{10})(?:/|$)',
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return ''

    # todo retry x-rate-limit
    def get_product(self, id_: str, locale: str = 'en_US', shipping_region: str = '', shipping_postal_code: str = '',
                    **kwargs) -> Product:
        # https://developer-docs.amazon.com/amazon-business/docs/product-search-api-v1-reference#retrieves-product-data-based-on-an-asin-identifier
        api_url = f"{self.api_endpoint}/products/2020-08-26/products/{id_}/"
        params = {
            'productRegion': self.region,
            'locale': locale,
            'shippingRegion': shipping_region,
            'shippingPostalCode': shipping_postal_code,
            'facets': 'OFFERS,IMAGES',
        }
        headers = self._get_headers()

        response = requests.get(api_url, params=params, headers=headers)
        if response.status_code != 200:
            self.logger.info(f"Request: {response.url}\nResponse: {response.content}")

        response.raise_for_status()

        product_data = response.json()

        variant_id_list = [variant['asin'] for variant in product_data['productVariations']['variations']]
        if variant_id_list:
            variants_data = self._get_variants(variant_id_list, locale, shipping_region, shipping_postal_code)
        else:
            variants_data = []

        return self._construct_product(product_data, variants_data)

    def _construct_product(self, product_data: dict, variants_data: list) -> Product:
        option_list = self._construct_options(product_data['productVariations']['dimensions'])
        variants_options_map = self._construct_variants_options_map(product_data['productVariations']['variations'],
                                                                    option_list)
        main_variant = self._construct_variant(product_data, variants_options_map)
        variant_list = self._construct_variants(variants_data, variants_options_map)
        return Product(
            provider=self.get_provider_name(),
            external_id=main_variant.external_id,
            title=main_variant.title,
            price=main_variant.price,
            images=main_variant.images,
            selected_options=main_variant.selected_options,
            shipping_cost=main_variant.shipping_cost,
            tax_amount=main_variant.tax_amount,
            offer_id=main_variant.offer_id,
            url=main_variant.url,
            options=option_list,
            variants=variant_list,
        )

    @staticmethod
    def _construct_options(options_data: list) -> list[Product.Option]:
        result = []
        for option_data in options_data:
            result.append(Product.Option.model_construct(
                name=option_data['displayString'],
                values=[value['displayString'] for value in option_data['dimensionValues']],
            ))
        return result

    @staticmethod
    def _construct_variants_options_map(variants_options_map: list, options: list[Product.Option]) -> dict[
        str, list[Product.SelectedOption]]:
        result = {}
        for variant in variants_options_map:
            variant_id = variant['asin']
            result[variant_id] = []
            for variant_option in variant['variationValues']:
                option = options[variant_option['index']]
                result[variant_id].append(Product.SelectedOption.model_construct(
                    name=option.name,
                    value=option.values[variant_option['value']],
                ))
        return result

    @staticmethod
    def _construct_variants(variants_data: list, variants_options_map: dict[str, list[Product.SelectedOption]]) -> list[
        Product.Variant]:
        variant_list = []
        for variant_data in variants_data:
            variant_list.append(AmazonProvider._construct_variant(variant_data, variants_options_map))
        return variant_list

    @staticmethod
    def _construct_variant(variant_data: dict,
                           variants_options_map: dict[str, list[Product.SelectedOption]]) -> Product.Variant:
        variant_id = variant_data['asin']
        images = variant_data['includedDataTypes']['IMAGES']
        offers = variant_data['includedDataTypes']['OFFERS']
        for offer in offers:
            offer_id = offer['offerId']

            price_data = offer['price']['value']
            if not price_data:
                continue
            price = Money(amount=price_data['amount'], currency=price_data['currencyCode'])

            shipping_options = offer['shippingOptions']
            for shipping_option in shipping_options:
                shipping_cost_data = shipping_option['shippingCost']['value']
                if shipping_cost_data:
                    shipping_cost = Money(amount=shipping_cost_data['amount'],
                                          currency=shipping_cost_data['currencyCode'])
                    break
            else:
                continue
            break
        else:
            offer_id = ''
            price = shipping_cost = None

        return Product.Variant.model_construct(
            external_id=variant_id,
            title=variant_data['title'],
            price=price,
            images=[image['large']['url'] for image in images],
            selected_options=variants_options_map[variant_id] if variants_options_map else [],
            shipping_cost=shipping_cost,
            tax_amount=Money(amount=Decimal('0.0'), currency='USD'),  # todo
            offer_id=offer_id,
            url=variant_data['url'],
        )

    def _get_variants(self, ids, locale: str = 'en_US', shipping_region: str = '', shipping_postal_code: str = ''):
        max_batch_size = 30
        result = []
        for i in range(0, len(ids), max_batch_size):
            data = self._get_variants_in_batch(ids[i:i + max_batch_size], locale, shipping_region, shipping_postal_code)
            result.extend(data['products'])
        return result

    # todo retry
    def _get_variants_in_batch(self, ids, locale: str, shipping_region: str = '', shipping_postal_code: str = ''):
        # 每次最多请求 30 个 asin
        # https://developer-docs.amazon.com/amazon-business/docs/product-search-api-v1-reference#the-result-for-list-of-asin-search
        api_url = f"{self.api_endpoint}/products/2020-08-26/products/getProductsByAsins"
        data = {
            'productIds': ids,
            'productRegion': self.region.upper(),
            'locale': locale,
            'shippingRegion': shipping_region or None,
            'shippingPostalCode': shipping_postal_code or None,
            'facets': ['OFFERS', 'IMAGES'],
        }
        headers = self._get_headers()

        response = requests.post(api_url, json=data, headers=headers)

        if response.status_code != 200:
            self.logger.info(f"Request: {response.url}\nResponse: {response.content}")

        response.raise_for_status()

        return response.json()

    def create_order(self, order: 'Order', **kwargs):
        api_url = f"{self.api_endpoint}/ordering/2022-10-30/orders"
        headers = self._get_headers()

        # Build the order request payload
        request_payload = self._build_order_request(order)

        try:
            response = requests.post(api_url, json=request_payload, headers=headers)

            # Log the request and response for debugging
            self.logger.info(f"Amazon Order Request: {request_payload}")
            self.logger.info(f"Amazon Order Response: {response.status_code} - {response.text}")

            if response.status_code == 200:
                # Parse successful response
                order_result = response.json()
                return self._process_order_result(order_result, order)

            elif response.status_code == 400:
                # Handle validation errors
                error_response = response.json()
                error_message = self._extract_error_message(error_response)
                raise ValidationError(f"订单创建失败: {error_message}。请联系我们的客服团队获取帮助。")

            elif response.status_code == 403:
                # Handle authorization errors
                raise ValidationError("订单创建失败: 授权错误。请联系我们的客服团队获取帮助。")

            elif response.status_code == 429:
                # Handle rate limiting
                raise ValidationError("订单创建失败: 请求过于频繁。请稍后重试或联系我们的客服团队。")

            else:
                # Handle other errors
                raise ValidationError(
                    f"订单创建失败: 服务暂时不可用 (错误代码: {response.status_code})。请联系我们的客服团队获取帮助。")

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Amazon API request failed: {str(e)}")
            raise ValidationError("订单创建失败: 网络连接错误。请联系我们的客服团队获取帮助。")
        except Exception as e:
            self.logger.error(f"Unexpected error in create_order: {str(e)}")
            raise ValidationError("订单创建失败: 系统错误。请联系我们的客服团队获取帮助。")

    def _build_order_request(self, order: "OrderType"):
        # expected order charge
        order_subtotal = order_tax = order_shipping = Money(amount=Decimal('0.0'), currency='USD')
        # Build line items from order.line_items
        line_items = []
        for line_item in order.line_items:
            line_items.append({
                "externalId": str(line_item.id),
                "quantity": 1,  # Each line item represents quantity 1
                "attributes": [
                    {
                        "attributeType": "SelectedProductReference",
                        "productReference": {
                            "productReferenceType": "ProductIdentifier",
                            "id": line_item.product_external_id
                        }
                    },
                    {
                        "attributeType": "SelectedBuyingOptionReference",
                        "buyingOptionReference": {
                            "buyingOptionReferenceType": "BuyingOptionIdentifier",
                            "id": line_item.product_offer_id
                        }
                    },
                ],
                "expectations": [
                    {
                        "expectationType": "ExpectedUnitPrice",
                        "amount": {
                            "currencyCode": line_item.base_price.currency,
                            "amount": float(line_item.base_price.amount),
                        }
                    },
                    {
                        "expectationType": "ExpectedCharge",
                        "amount": {
                            "currencyCode": line_item.base_price.currency,
                            "amount": float(line_item.base_price.amount),
                        },
                        "source": "SUBTOTAL"
                    },
                ]
            })
            order_subtotal += line_item.base_price
            order_tax += line_item.tax_amount
            order_shipping += line_item.shipping_cost

        # Build order attributes
        order_attributes = [
            {
                "attributeType": "Region",
                "region": self.region
            },
            {
                "attributeType": "SelectedPaymentMethodReference",
                "paymentMethodReference": {
                    "paymentMethodReferenceType": "StoredPaymentMethod"
                }
            },
            {
                "attributeType": "BuyingGroupReference",
                "groupReference": {
                    "groupReferenceType": "GroupIdentity",
                    "identifier": settings.AMAZON_BUSINESS_GROUP_ID,
                }
            },
            {
                "attributeType": "BuyerReference",
                "userReference": {
                    "userReferenceType": "UserEmail",
                    "emailAddress": settings.AMAZON_BUSINESS_USER_EMAIL,
                }
            },
            {
                "attributeType": "ShippingAddress",
                "address": {
                    "addressType": "PhysicalAddress",
                    "fullName": order.address.name,
                    "phoneNumber": order.address.phone,
                    "addressLine1": order.address.line_1,
                    "addressLine2": order.address.line_2,
                    "city": order.address.city,
                    "stateOrRegion": order.address.state,
                    "postalCode": order.address.postal_code,
                    "countryCode": order.address.country
                }
            },
            {
                "attributeType": "PurchaseOrderNumber",
                "purchaseOrderNumber": str(order.id)
            },
        ]
        if settings.DEBUG:
            order_attributes.append({
                "attributeType": "TrialMode"
            })

        order_expectations = [
            {
                "expectationType": "ExpectedCharge",
                "amount": {
                    "currencyCode": order_subtotal.currency,
                    "amount": float(order_subtotal.amount),
                },
                "source": "SUBTOTAL"
            },
            {
                "expectationType": "ExpectedCharge",
                "amount": {
                    "currencyCode": order_tax.currency,
                    "amount": float(order_tax.amount),
                },
                "source": "TAX"
            },
            {
                "expectationType": "ExpectedCharge",
                "amount": {
                    "currencyCode": order_shipping.currency,
                    "amount": float(order_shipping.amount),
                },
                "source": "SHIPPING"
            },
        ]

        return {
            "externalId": str(order.id),
            "lineItems": line_items,
            "attributes": order_attributes,
            "expectations": [], # order_expectations,
            # "x-amz-user-email": settings.AMAZON_BUSINESS_USER_EMAIL,
        }

    def _process_order_result(self, order_result, order):
        """
        Process Amazon Business API order result

        Args:
            order_result: Amazon API response
            order: Original order instance

        Returns:
            dict: Processed order result

        Raises:
            ValidationError: When order has rejections that require user attention
        """
        from fanwish.common.exceptions import ValidationError

        # Check for any rejected items
        rejected_items = []
        accepted_items = []

        for line_item in order_result.get('lineItems', []):
            rejected_items.extend(line_item.get('rejectedItems', []))
            accepted_items.extend(line_item.get('acceptedItems', []))

        # If there are rejected items, analyze the rejection reasons
        if rejected_items:
            rejection_reasons = self._analyze_rejections(rejected_items)

            # If all items were rejected, raise an error
            if not accepted_items:
                raise ValidationError(f"订单无法完成: {rejection_reasons}。请联系我们的客服团队获取帮助。")

            # If partial rejection, log warning but continue
            self.logger.warning(f"Order {order.id} partially rejected: {rejection_reasons}")

        # Extract order identifiers and tracking info
        order_identifiers = []
        tracking_info = []

        for line_item in order_result.get('lineItems', []):
            for accepted_item in line_item.get('acceptedItems', []):
                for artifact in accepted_item.get('artifacts', []):
                    if artifact.get('acceptanceArtifactType') == 'OrderIdentifier':
                        order_identifiers.append(artifact.get('identifier'))
                    elif artifact.get('acceptanceArtifactType') == 'ShipmentGroup':
                        tracking_info.append({
                            'shipment_id': artifact.get('identifier'),
                            'tracking_numbers': [
                                ref.get('value') for ref in artifact.get('packageReferences', [])
                                if ref.get('packageReferenceType') == 'CarrierTrackingNumber'
                            ]
                        })

        result = {
            'success': True,
            'amazon_order_ids': order_identifiers,
            'tracking_info': tracking_info,
            'accepted_items': accepted_items,
            'rejected_items': rejected_items,
            'raw_response': order_result
        }
        print(result)

        return result

    def _analyze_rejections(self, rejected_items):
        """
        Analyze rejection artifacts to provide user-friendly error messages

        Args:
            rejected_items: List of rejected items from Amazon API

        Returns:
            str: User-friendly error message
        """
        rejection_messages = []

        for rejected_item in rejected_items:
            for artifact in rejected_item.get('artifacts', []):
                artifact_type = artifact.get('rejectionArtifactType')

                if artifact_type == 'BrokenUnitPriceExpectation':
                    # Price increased beyond acceptable limit
                    boundary = artifact.get('boundary', {})
                    actual = artifact.get('actualAmount', {})
                    rejection_messages.append(
                        f"商品价格已上涨至 {actual.get('amount', 'N/A')} {actual.get('currencyCode', '')}"
                        f"，超出预期价格 {boundary.get('amount', 'N/A')} {boundary.get('currencyCode', '')}"
                    )

                elif artifact_type == 'BrokenChargeExpectation':
                    # Total charges exceeded expected limit
                    boundary = artifact.get('boundary', {})
                    actual = artifact.get('actualAmount', {})
                    rejection_messages.append(
                        f"订单总费用 {actual.get('amount', 'N/A')} {actual.get('currencyCode', '')} "
                        f"超出预期费用 {boundary.get('amount', 'N/A')} {boundary.get('currencyCode', '')}"
                    )

                elif artifact_type == 'UnavailableQuantity':
                    # Item out of stock
                    quantity = artifact.get('quantity', 0)
                    rejection_messages.append(f"商品库存不足，缺少 {quantity} 件")

                elif artifact_type == 'DeliveryTimeRangeIncongruity':
                    # Delivery time outside expected range
                    rejection_messages.append("配送时间超出预期范围")

                elif artifact_type == 'AgeRestriction':
                    # Age restriction violation
                    min_age = artifact.get('minimumAge', 0)
                    rejection_messages.append(f"商品有年龄限制，需满 {min_age} 岁")

                elif artifact_type == 'RejectionMessage':
                    # Generic rejection message
                    message = artifact.get('message', '')
                    if message:
                        rejection_messages.append(message)

                elif artifact_type == 'RejectionCode':
                    # Map common rejection codes to user-friendly messages
                    code = artifact.get('code', '')
                    code_messages = {
                        '008-007': '库存不足或商品不符合购买条件',
                        '008-008': '商品缺货',
                        # Add more codes as needed
                    }
                    if code in code_messages:
                        rejection_messages.append(code_messages[code])

        if not rejection_messages:
            return "订单被拒绝，原因未知"

        return "；".join(set(rejection_messages))  # Remove duplicates and join

    def _extract_error_message(self, error_response):
        """
        Extract user-friendly error message from Amazon API error response

        Args:
            error_response: Amazon API error response

        Returns:
            str: User-friendly error message
        """
        errors = error_response.get('errors', [])
        if not errors:
            return "未知错误"

        error_messages = []
        for error in errors:
            code = error.get('code', '')
            message = error.get('message', '')
            details = error.get('details', '')

            # Map common error codes to user-friendly messages
            if code == 'INVALID_REQUEST_PARAMETER':
                error_messages.append("请求参数无效")
            elif code == 'UNAUTHORIZED':
                error_messages.append("授权失败")
            elif code == 'INSUFFICIENT_INVENTORY':
                error_messages.append("库存不足")
            elif code == 'PRICE_CHANGED':
                error_messages.append("商品价格已变更")
            elif code == 'SHIPPING_RESTRICTION':
                error_messages.append("配送限制")
            elif message:
                error_messages.append(message)
            elif details:
                error_messages.append(details)

        return "；".join(error_messages) if error_messages else "请求处理失败"

    def _get_headers(self) -> dict:
        return {
            'Content-Type': 'application/json',
            'x-amz-access-token': self._get_access_token(),
            'x-amz-user-email': settings.AMAZON_BUSINESS_USER_EMAIL,
            'x-amz-date': timezone.now().strftime('%Y%m%dT%H%M%SZ'),
        }

    def _get_access_token(self) -> str:
        if not self.access_token:
            self.access_token = products_cache.get(
                products_cache.make_amazon_business_api_access_token_key(self.region))
            if not self.access_token:
                # todo add lock
                self.access_token = self._refresh_access_token()
        return self.access_token

    # todo retry rate limit
    def _refresh_access_token(self) -> str:
        # https://developer-docs.amazon.com/amazon-business/docs/refreshing-access-tokens
        response = requests.post(
            'https://api.amazon.com/auth/O2/token',
            data={
                'grant_type': 'refresh_token',
                'refresh_token': self.refresh_token,
                'client_id': settings.AMAZON_BUSINESS_CLIENT_ID,
                'client_secret': settings.AMAZON_BUSINESS_CLIENT_SECRET,
            },
        )
        response.raise_for_status()

        token_data = response.json()
        access_token = token_data['access_token']
        expires_in = token_data['expires_in']

        products_cache.set(
            products_cache.make_amazon_business_api_access_token_key(self.region),
            value=access_token,
            timeout=expires_in - 300,
        )

        return access_token


def get_product_provider_by_url(url: str) -> ProductProvider:
    if not url.lower().startswith('http'):
        url = f"https://{url}"

    domain = urlparse(url).netloc

    if not domain:
        raise InvalidProductURLError()

    if 'amazon.com' in domain:
        return AmazonProvider(AmazonProvider.Region.US)
    elif 'amazon.ca' in domain:
        return AmazonProvider(AmazonProvider.Region.CA)
    elif 'amazon.es' in domain:
        return AmazonProvider(AmazonProvider.Region.ES)
    elif 'amazon.co.uk' in domain:
        return AmazonProvider(AmazonProvider.Region.UK)
    elif 'amazon.fr' in domain:
        return AmazonProvider(AmazonProvider.Region.FR)
    elif 'amazon.de' in domain:
        return AmazonProvider(AmazonProvider.Region.DE)
    elif 'amazon.it' in domain:
        return AmazonProvider(AmazonProvider.Region.IT)
    elif 'amazon.co.jp' in domain:
        return AmazonProvider(AmazonProvider.Region.JP)
    else:
        raise UnsupportedProductPlatformError()


def get_product_provider_by_name(name: str) -> ProductProvider:
    if name.startswith('amazon_'):
        return AmazonProvider(name.split('_')[1])
    else:
        raise UnsupportedProductPlatformError()
