# ruff: noqa: ERA001, E501
"""Base settings to build other settings files upon."""
from decimal import Decimal
from pathlib import Path

import environ
from corsheaders.defaults import default_headers as default_cors_headers

BASE_DIR = Path(__file__).resolve(strict=True).parent.parent.parent
# fanwish/
APPS_DIR = BASE_DIR / "fanwish"
env = environ.Env()

READ_DOT_ENV_FILE = env.bool("DJANGO_READ_DOT_ENV_FILE", default=False)
if READ_DOT_ENV_FILE:
    # OS environment variables take precedence over variables from .env
    env.read_env(str(BASE_DIR / ".env"))

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJANGO_DEBUG", False)
# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
TIME_ZONE = "UTC"
# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "en-us"
# https://docs.djangoproject.com/en/dev/ref/settings/#languages
# from django.utils.translation import gettext_lazy as _
# LANGUAGES = [
#     ('en', _('English')),
#     ('fr-fr', _('French')),
#     ('pt-br', _('Portuguese')),
# ]
# https://docs.djangoproject.com/en/dev/ref/settings/#use-i18n
USE_I18N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# https://docs.djangoproject.com/en/dev/ref/settings/#locale-paths
LOCALE_PATHS = [str(BASE_DIR / "locale")]

# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases
DATABASES = {"default": env.db("DATABASE_URL")}
DATABASES["default"]["ATOMIC_REQUESTS"] = True
# https://docs.djangoproject.com/en/stable/ref/settings/#std:setting-DEFAULT_AUTO_FIELD
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "config.urls"
# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "config.wsgi.application"

# APPS
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.admin",
    "django.forms",
]
THIRD_PARTY_APPS = [
    "crispy_forms",
    "crispy_bootstrap5",
    "rest_framework",
    "rest_framework.authtoken",
    "corsheaders",
    "drf_spectacular",
    "anymail",
    "django_filters",
    "jsoneditor",
]

LOCAL_APPS = [
    "fanwish.settings",
    "fanwish.users",
    "fanwish.products",
    "fanwish.addresses",
    "fanwish.gifts",
    "fanwish.orders",
    "fanwish.customers",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# AUTHENTICATION
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#authentication-backends
AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-user-model
AUTH_USER_MODEL = "users.User"

# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    # https://docs.djangoproject.com/en/dev/topics/auth/passwords/#using-argon2-with-django
    "django.contrib.auth.hashers.Argon2PasswordHasher",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
        "OPTIONS": {
            "min_length": 6,
        },
    },
]

# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# STORAGES
# ------------------------------------------------------------------------------
STORAGES = {
    "default": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# STATIC
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#static-root
STATIC_ROOT = str(BASE_DIR / "staticfiles")
# https://docs.djangoproject.com/en/dev/ref/settings/#static-url
STATIC_URL = "/static/"
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#std:setting-STATICFILES_DIRS
STATICFILES_DIRS = [str(APPS_DIR / "static")]
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#staticfiles-finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = str(APPS_DIR / "media")
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/media/"

# TEMPLATES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#templates
TEMPLATES = [
    {
        # https://docs.djangoproject.com/en/dev/ref/settings/#std:setting-TEMPLATES-BACKEND
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        # https://docs.djangoproject.com/en/dev/ref/settings/#dirs
        "DIRS": [str(APPS_DIR / "templates")],
        # https://docs.djangoproject.com/en/dev/ref/settings/#app-dirs
        "APP_DIRS": True,
        "OPTIONS": {
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-context-processors
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# https://docs.djangoproject.com/en/dev/ref/settings/#form-renderer
FORM_RENDERER = "django.forms.renderers.TemplatesSetting"

# http://django-crispy-forms.readthedocs.io/en/latest/install.html#template-packs
CRISPY_TEMPLATE_PACK = "bootstrap5"
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"

# FIXTURES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#fixture-dirs
FIXTURE_DIRS = (str(APPS_DIR / "fixtures"),)

# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-httponly
SESSION_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-httponly
CSRF_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#x-frame-options
X_FRAME_OPTIONS = "DENY"

# CSRF_TRUSTED_ORIGINS
CSRF_TRUSTED_ORIGINS = ["https://wp8000.tjo.apphi.com:44443"]

# Anymail
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#email-backend
# https://anymail.readthedocs.io/en/stable/installation/#anymail-settings-reference
# https://anymail.readthedocs.io/en/stable/esps/amazon_ses/
EMAIL_BACKEND = "anymail.backends.amazon_ses.EmailBackend"
# https://docs.djangoproject.com/en/dev/ref/settings/#email-timeout
EMAIL_TIMEOUT = 5
# https://docs.djangoproject.com/en/dev/ref/settings/#default-from-email
DEFAULT_FROM_EMAIL = env(
    "DJANGO_DEFAULT_FROM_EMAIL",
    default="FanWish <<EMAIL>>",
)
# https://docs.djangoproject.com/en/dev/ref/settings/#server-email
SERVER_EMAIL = env("DJANGO_SERVER_EMAIL", default=DEFAULT_FROM_EMAIL)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-subject-prefix
EMAIL_SUBJECT_PREFIX = env(
    "DJANGO_EMAIL_SUBJECT_PREFIX",
    default="[FanWish] ",
)
ACCOUNT_EMAIL_SUBJECT_PREFIX = EMAIL_SUBJECT_PREFIX
ANYMAIL = {
    "AMAZON_SES_CONFIGURATION_SET_NAME": "system",
    "AMAZON_SES_CLIENT_PARAMS": {
        "region_name": "us-west-2",
    },
}

# ADMIN
# ------------------------------------------------------------------------------
# Django Admin URL.
ADMIN_URL = "admin/"
# https://docs.djangoproject.com/en/dev/ref/settings/#admins
ADMINS = [("""Peng Wang""", "<EMAIL>")]
# https://docs.djangoproject.com/en/dev/ref/settings/#managers
MANAGERS = ADMINS

# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#logging
# See https://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOG_DIR_PATH = BASE_DIR / "logs"
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s",
        },
        "json": {"class": "fanwish.utils.logging.JsonLogFormatter"},
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
        "app_logfile": {
            "level": "INFO",
            "class": "logging.handlers.WatchedFileHandler",
            "filename": f"{LOG_DIR_PATH}/app.log",
            "formatter": "json",
        },
        "task_logfile": {
            "level": "INFO",
            "class": "logging.handlers.WatchedFileHandler",
            "filename": f"{LOG_DIR_PATH}/task.log",
            "formatter": "json",
        },
        "stripe_logfile": {
            "level": "INFO",
            "class": "logging.handlers.WatchedFileHandler",
            "filename": f"{LOG_DIR_PATH}/stripe.log",
            "formatter": "json",
        },
    },
    "root": {"level": "INFO", "handlers": ["console", "app_logfile"]},
    "loggers": {
        "app": {
            "level": "INFO",
            "handlers": ["app_logfile"],
            "propagate": False,
        },
        "task": {
            "level": "INFO",
            "handlers": ["task_logfile"],
            "propagate": False,
        },
        "stripe": {
            "level": "INFO",
            "handlers": ["stripe_logfile"],
            "propagate": False,
        },
        # Errors logged by the SDK itself
        "sentry_sdk": {"level": "ERROR", "handlers": ["console"], "propagate": False},
        "django.db.backends": {
            "level": "ERROR",
            "handlers": ["console", "app_logfile"],
            "propagate": False,
        },
    },
}

# Cache
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#caches
REDIS_URL = env("REDIS_URL")


def make_redis_cache_setting(database):
    return {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"{REDIS_URL}/{database}",
        "TIMEOUT": None,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # Mimicing memcache behavior.
            # https://github.com/jazzband/django-redis#memcached-exceptions-behavior
            "IGNORE_EXCEPTIONS": True,
        },
    }


CACHES = {
    "default": make_redis_cache_setting(0),
    "settings": make_redis_cache_setting(1),
    "gift_items": make_redis_cache_setting(2),
    "products": make_redis_cache_setting(3),
    "customers": make_redis_cache_setting(4),
}

# django-rest-framework
# -------------------------------------------------------------------------------
# django-rest-framework - https://www.django-rest-framework.org/api-guide/settings/
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.TokenAuthentication",
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "EXCEPTION_HANDLER": "fanwish.common.exceptions.custom_exception_handler",
    "DATETIME_FORMAT": "%Y-%m-%dT%H:%M:%S%z",
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
    "DEFAULT_FILTER_BACKENDS": ("django_filters.rest_framework.DjangoFilterBackend",),
}

# django-cors-headers - https://github.com/adamchainz/django-cors-headers#setup
CORS_URLS_REGEX = r"^/api/.*$"
CORS_ALLOW_HEADERS = (
    *default_cors_headers,
    "x-app-id",
    "x-app-version",
)
CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^https://fanwish\.me$",
    r"^https://[\w-]+\.fanwish\.me$",
    r"^https://[\w-]+\.vercel\.app$",
    r"^https?://localhost:\d+",
    r"^https?://192\.168\.\d+\.\d+:\d+",
    r"^https://[\w-]+\.tjo\.apphi\.com:44443",
]

# Temporary email list
# -------------------------------------------------------------------------------
TMP_EMAIL_LIST_FILE = BASE_DIR / "files" / "tmp_email_list.txt"

# Snowflake GifItem & Order ID 生成算法
# -------------------------------------------------------------------------------
SNOWFLAKE_EPOCH = 1750000000000  # 2025-06-15T15:06:40+00:00

# Amazon Business
# -------------------------------------------------------------------------------
AMAZON_BUSINESS_CLIENT_ID = env("AMAZON_BUSINESS_CLIENT_ID")
AMAZON_BUSINESS_CLIENT_SECRET = env("AMAZON_BUSINESS_CLIENT_SECRET")
AMAZON_BUSINESS_API_REFRESH_TOKENS = {
    "US": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_US"),
    "CA": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_CA"),
    "UK": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_UK"),
    "ES": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_ES"),
    "FR": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_FR"),
    "DE": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_DE"),
    "IT": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_IT"),
    "JP": env("AMAZON_BUSINESS_API_REFRESH_TOKEN_JP"),
}
AMAZON_BUSINESS_USER_EMAIL = "<EMAIL>"
AMAZON_BUSINESS_GROUP_ID = "A3PZ88R4MOOD9K"
AMAZON_PAYMENT_METHOD_ID = env("AMAZON_PAYMENT_METHOD_ID", default="")

# Stripe
# -------------------------------------------------------------------------------
STRIPE_SECRET_KEY = env("STRIPE_SECRET_KEY",
                        default="sk_test_51PMNeUH8wWOdKjf61PnkMdVgGQ0rMxsJ6rFBsJX36fcu4OPWxVRqA35MqjXYvvgNPG3eMI7Nf7hNpJFzqRglJuoP00NEhY9Ueu")
STRIPE_WEBHOOK_SECRET = env("STRIPE_WEBHOOK_SECRET",
                           default="whsec_B8Dx5ge1uvqJopb2XtOis1uTaDqDQcSC")

# APP
# -------------------------------------------------------------------------------
SITE_URL = env("SITE_URL", default="https://api.fanwish.me")
WEB_URL = env("WEB_URL", default="https://fanwish.me")
PLATFORM_MARKUP_PERCENTAGE = Decimal('0.1')  # 10%
SERVICE_FEE_PERCENTAGE = Decimal('0.1')  # 10%
