from django.conf import settings
from django.urls import path
from rest_framework.routers import DefaultRouter
from rest_framework.routers import SimpleRouter

from fanwish.addresses.api.views import AddressViewSet
from fanwish.customers.api.views import PublicCustomerViewSet
from fanwish.gifts.api.views import GiftItemViewSet, PublicGiftItemViewSet
from fanwish.orders.api.views import PublicOrderViewSet
from fanwish.products.api.views import ProductViewSet
from fanwish.settings.api.views import SettingViewSet
from fanwish.users.api.views import UserViewSet, PublicUserViewSet
from fanwish.webhooks.api.order_payments import OrderPaymentWebhookAPIView

router = DefaultRouter(trailing_slash=False) if settings.DEBUG else SimpleRouter(trailing_slash=False)

router.register("settings", SettingViewSet)
router.register("users", UserViewSet)
router.register("products", ProductViewSet)
router.register("addresses", AddressViewSet)
router.register("gifts", GiftItemViewSet)
router.register("orders", OrderViewSet)

# Public access
router.register("public/users", PublicCustomerViewSet, basename="public-customers")
router.register("public/creators", PublicUserViewSet, basename="public-users")
router.register("public/wishlists", PublicGiftItemViewSet, basename="public-wishlists")
router.register("public/orders", PublicOrderViewSet, basename="public-orders")

app_name = "api"
urlpatterns = router.urls + [
    path("webhooks/order-payments", OrderPaymentWebhookAPIView.as_view(), name="webhooks-order-payments"),
]
